import { Column, Entity, PrimaryGeneratedColumn, Join<PERSON><PERSON>umn, OneToOne } from 'typeorm';
import { ProductTypeEnum } from '@modules/business/enums';
import { UserProduct } from './user-product.entity';

/**
 * Entity đại diện cho bảng product_advanced_info trong cơ sở dữ liệu
 * Bảng chứa thông tin nâng cao cho các loại sản phẩm (DIGITAL, EVENT, SERVICE, COMBO)
 */
@Entity('product_advanced_info')
export class ProductAdvancedInfo {
  /**
   * ID của bản ghi thông tin nâng cao
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của sản phẩm liên kết
   */
  @Column({ name: 'product_id', type: 'bigint', nullable: false, comment: 'ID của sản phẩm liên kết' })
  productId: number;

  /**
   * <PERSON>ạ<PERSON> sản phẩm
   */
  @Column({
    name: 'product_type',
    type: 'enum',
    enum: ProductTypeEnum,
    nullable: false,
    comment: '<PERSON>ại sản phẩm (DIGITAL, EVENT, SERVICE, COMBO)'
  })
  productType: ProductTypeEnum;

  /**
   * Số lượt mua
   */
  @Column({ 
    name: 'purchase_count', 
    type: 'integer', 
    default: 0, 
    nullable: false, 
    comment: 'Số lượt mua sản phẩm' 
  })
  purchaseCount: number;

  /**
   * Quy trình xử lý đơn hàng số (cho sản phẩm DIGITAL)
   */
  @Column({ 
    name: 'digital_fulfillment_flow', 
    type: 'jsonb', 
    nullable: true, 
    comment: 'Quy trình xử lý đơn hàng số (JSON)' 
  })
  digitalFulfillmentFlow: any;

  /**
   * Đầu ra sản phẩm số (cho sản phẩm DIGITAL)
   */
  @Column({ 
    name: 'digital_output', 
    type: 'jsonb', 
    nullable: true, 
    comment: 'Đầu ra sản phẩm số (JSON)' 
  })
  digitalOutput: any;

  /**
   * Hình thức tổ chức sự kiện (cho sản phẩm EVENT)
   */
  @Column({ 
    name: 'event_format', 
    type: 'varchar', 
    length: 50, 
    nullable: true, 
    comment: 'Hình thức tổ chức sự kiện (ONLINE, OFFLINE, HYBRID)' 
  })
  eventFormat: string;

  /**
   * Đường dẫn tham gia sự kiện (cho sự kiện online)
   */
  @Column({ 
    name: 'event_link', 
    type: 'text', 
    nullable: true, 
    comment: 'Đường dẫn tham gia sự kiện online' 
  })
  eventLink: string;

  /**
   * Địa điểm tham gia sự kiện (cho sự kiện offline)
   */
  @Column({ 
    name: 'event_location', 
    type: 'text', 
    nullable: true, 
    comment: 'Địa điểm tham gia sự kiện offline' 
  })
  eventLocation: string;

  /**
   * Ngày bắt đầu sự kiện (timestamp)
   */
  @Column({ 
    name: 'start_date', 
    type: 'bigint', 
    nullable: true, 
    comment: 'Ngày bắt đầu sự kiện (timestamp)' 
  })
  startDate: number;

  /**
   * Ngày kết thúc sự kiện (timestamp)
   */
  @Column({ 
    name: 'end_date', 
    type: 'bigint', 
    nullable: true, 
    comment: 'Ngày kết thúc sự kiện (timestamp)' 
  })
  endDate: number;

  /**
   * Múi giờ
   */
  @Column({ 
    name: 'timezone', 
    type: 'varchar', 
    length: 100, 
    nullable: true, 
    comment: 'Múi giờ' 
  })
  timezone: string;

  /**
   * Danh sách loại vé sự kiện (cho sản phẩm EVENT)
   */
  @Column({ 
    name: 'ticket_types', 
    type: 'jsonb', 
    nullable: true, 
    comment: 'Danh sách loại vé sự kiện (JSON)' 
  })
  ticketTypes: any;

  /**
   * Danh sách gói dịch vụ (cho sản phẩm SERVICE)
   */
  @Column({ 
    name: 'service_packages', 
    type: 'jsonb', 
    nullable: true, 
    comment: 'Danh sách gói dịch vụ (JSON)' 
  })
  servicePackages: any;

  /**
   * Thời gian tạo (millis)
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    nullable: false, 
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint", 
    comment: 'Thời gian tạo (millis)' 
  })
  createdAt: number;

  /**
   * Thời gian cập nhật (millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    nullable: false,
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint",
    comment: 'Thời gian cập nhật (millis)'
  })
  updatedAt: number;

  /**
   * Danh sách hình ảnh cho advanced info (ticket types, service packages)
   */
  @Column({
    name: 'images',
    type: 'jsonb',
    nullable: false,
    default: () => "'[]'::jsonb",
    comment: 'Danh sách hình ảnh cho advanced info (JSON)'
  })
  images: any;

  /**
   * Thông tin combo sản phẩm (cho sản phẩm COMBO)
   */
  @Column({
    name: 'combo',
    type: 'jsonb',
    nullable: false,
    default: () => `'{"info": []}'::jsonb`,
    comment: 'Thông tin combo sản phẩm chứa danh sách sản phẩm con và số lượng (JSON)'
  })
  combo: { info: Array<{ productId: number; total: number }> };

  /**
   * Relationship với UserProduct
   */
  @OneToOne(() => UserProduct, userProduct => userProduct.advancedInfo)
  @JoinColumn({ name: 'product_id' })
  product: UserProduct;
}
