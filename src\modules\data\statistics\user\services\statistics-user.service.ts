import { Injectable, Logger } from '@nestjs/common';
import { UserStorageResponseDto } from '../../dto/statistics-response.dto';
import { plainToInstance } from 'class-transformer';
import { UsageRepository } from '../../repositories/usage.repository';
import { FileSizeFormatter } from '@shared/utils';
import { AppException } from '@common/exceptions';
import { STATISTICS_ERROR_CODES } from '../../exceptions/statistics.exception';
import { Usage } from '../../entities/usage.entity';

/**
 * Service xử lý các thống kê dữ liệu cho user
 * Chỉ sử dụng dữ liệu từ bảng usages, không tính toán từ các bảng khác
 */
@Injectable()
export class StatisticsUserService {
  private readonly logger = new Logger(StatisticsUserService.name);

  constructor(
    private readonly usageRepository: UsageRepository,
  ) {}

  /**
   * <PERSON><PERSON>y thống kê dung lượng dữ liệu của một người dùng từ bảng usages
   * @param userId ID của người dùng
   * @returns Promise<UserStorageResponseDto> Thống kê dung lượng dữ liệu
   */
  async getUserStorageUsage(userId: number): Promise<UserStorageResponseDto> {
    this.logger.log(`User ${userId} đang lấy thống kê dung lượng dữ liệu từ bảng usages...`);

    // Lấy thông tin usage từ bảng usages
    const usage = await this.usageRepository.findByUserAndCategory(userId, 'DATA');
    if (!usage) {
      this.logger.warn(`Không tìm thấy thông tin usage cho user ${userId}`);
      throw new AppException(
        STATISTICS_ERROR_CODES.USER_USAGE_NOT_FOUND,
        `Không tìm thấy thông tin sử dụng dung lượng cho người dùng ${userId}`
      );
    }

    try {
      // Tính phần trăm sử dụng
      const usagePercentage = usage.usageLimit > 0 ? (usage.currentUsage / usage.usageLimit) * 100 : 0;

      this.logger.log(
        `User ${userId} - Thống kê từ DB: ` +
        `Limit=${FileSizeFormatter.formatBytes(usage.usageLimit)}, ` +
        `Current=${FileSizeFormatter.formatBytes(usage.currentUsage)}, ` +
        `Remaining=${FileSizeFormatter.formatBytes(usage.remainingValue)}, ` +
        `Percentage=${usagePercentage.toFixed(2)}%`
      );

      // Chuyển đổi sang DTO - chỉ sử dụng dữ liệu từ bảng usages
      return plainToInstance(
        UserStorageResponseDto,
        {
          usageLimit: usage.usageLimit,
          usageLimitFormatted: FileSizeFormatter.formatBytes(usage.usageLimit),
          currentUsage: usage.currentUsage,
          currentUsageFormatted: FileSizeFormatter.formatBytes(usage.currentUsage),
          remainingValue: usage.remainingValue,
          remainingValueFormatted: FileSizeFormatter.formatBytes(usage.remainingValue),
          usageUnit: usage.usageUnit,
          usagePercentage: Math.round(usagePercentage * 100) / 100,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(`User ${userId} - Lỗi khi lấy thống kê dung lượng dữ liệu: ${error.message}`, error.stack);
      throw new AppException(
        STATISTICS_ERROR_CODES.STATISTICS_SERVICE_ERROR,
        `Lỗi dịch vụ khi lấy thống kê cho user ${userId}: ${error.message}`
      );
    }
  }

  /**
   * Lấy tất cả thông tin usage của user theo các category khác nhau
   * @param userId ID của người dùng
   * @returns Promise<Usage[]> Danh sách usage theo category
   */
  async getAllUserUsages(userId: number): Promise<Usage[]> {
    this.logger.log(`User ${userId} đang lấy tất cả thông tin usage...`);

    try {
      const usages = await this.usageRepository.findAllByUser(userId);

      this.logger.log(`User ${userId} có ${usages.length} bản ghi usage`);

      return usages;
    } catch (error) {
      this.logger.error(`User ${userId} - Lỗi khi lấy tất cả usage: ${error.message}`, error.stack);
      throw new AppException(
        STATISTICS_ERROR_CODES.STATISTICS_SERVICE_ERROR,
        `Lỗi khi lấy tất cả thông tin usage cho user ${userId}: ${error.message}`
      );
    }
  }


}
