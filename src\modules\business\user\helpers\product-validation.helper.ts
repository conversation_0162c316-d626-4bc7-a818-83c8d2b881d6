import { Injectable, Logger } from '@nestjs/common';
import { ProductTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { DigitalProductDto, EventProductDto, ServiceProductDto, ComboProductDto } from '../dto';

/**
 * Helper để validate thông tin sản phẩm nâng cao
 */
@Injectable()
export class ProductValidationHelper {
  private readonly logger = new Logger(ProductValidationHelper.name);

  /**
   * Validate thông tin nâng cao theo loại sản phẩm
   * @param productType Loại sản phẩm
   * @param advancedInfo Thông tin nâng cao
   * @throws AppException nếu validation thất bại
   */
  validateAdvancedInfo(
    productType: ProductTypeEnum,
    advancedInfo?: DigitalProductDto | EventProductDto | ServiceProductDto | ComboProductDto
  ): void {
    try {
      this.logger.log(`Validate thông tin nâng cao cho loại sản phẩm: ${productType}`);

      // PHYSICAL không được có thông tin nâng cao
      if (productType === ProductTypeEnum.PHYSICAL) {
        if (advancedInfo) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND, // Sử dụng error code có sẵn
            `Loại sản phẩm ${productType} không được có thông tin nâng cao`
          );
        }
        return;
      }

      // DIGITAL, EVENT, SERVICE, COMBO có thể có thông tin nâng cao (optional)
      if (!advancedInfo) {
        return; // Không bắt buộc phải có
      }

      // Validate theo từng loại
      switch (productType) {
        case ProductTypeEnum.DIGITAL:
          this.validateDigitalProduct(advancedInfo as DigitalProductDto);
          break;

        case ProductTypeEnum.EVENT:
          this.validateEventProduct(advancedInfo as EventProductDto);
          break;

        case ProductTypeEnum.SERVICE:
          this.validateServiceProduct(advancedInfo as ServiceProductDto);
          break;

        case ProductTypeEnum.COMBO:
          this.validateComboProduct(advancedInfo as ComboProductDto);
          break;

        default:
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND, // Sử dụng error code có sẵn
            `Loại sản phẩm ${productType} không hợp lệ`
          );
      }

      this.logger.log(`Validation thành công cho loại sản phẩm: ${productType}`);
    } catch (error) {
      this.logger.error(`Lỗi validation thông tin nâng cao: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Validate thông tin sản phẩm số
   */
  private validateDigitalProduct(digitalInfo: DigitalProductDto): void {
    // Validate digitalFulfillmentFlow
    if (!digitalInfo.digitalFulfillmentFlow) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Quy trình xử lý đơn hàng số là bắt buộc'
      );
    }

    const flow = digitalInfo.digitalFulfillmentFlow;
    if (!flow.deliveryMethod || !flow.deliveryTiming || !flow.accessStatus) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Thông tin quy trình xử lý đơn hàng số không đầy đủ'
      );
    }

    // Validate digitalOutput
    if (!digitalInfo.digitalOutput) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Thông tin đầu ra sản phẩm số là bắt buộc'
      );
    }

    const output = digitalInfo.digitalOutput;
    if (!output.outputType) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Loại đầu ra sản phẩm số là bắt buộc'
      );
    }
  }

  /**
   * Validate thông tin sự kiện
   */
  private validateEventProduct(eventInfo: EventProductDto): void {
    // Validate thông tin cơ bản
    if (!eventInfo.eventFormat || !eventInfo.startDate || !eventInfo.endDate || !eventInfo.timezone) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Thông tin cơ bản của sự kiện không đầy đủ (format, startDate, endDate, timezone)'
      );
    }

    // Validate thời gian
    if (eventInfo.startDate >= eventInfo.endDate) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc'
      );
    }

    // Validate theo format
    switch (eventInfo.eventFormat) {
      case 'ONLINE':
        if (!eventInfo.eventLink) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Sự kiện online bắt buộc phải có đường dẫn tham gia'
          );
        }
        break;

      case 'OFFLINE':
        if (!eventInfo.eventLocation) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Sự kiện offline bắt buộc phải có địa điểm'
          );
        }
        break;

      case 'HYBRID':
        if (!eventInfo.eventLink || !eventInfo.eventLocation) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            'Sự kiện hybrid bắt buộc phải có cả đường dẫn và địa điểm'
          );
        }
        break;
    }

    // Validate ticketTypes nếu có
    if (eventInfo.ticketTypes && eventInfo.ticketTypes.length > 0) {
      // Validate từng ticket type
      eventInfo.ticketTypes.forEach((ticket, index) => {
        if (ticket.startTime >= ticket.endTime) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            `Loại vé thứ ${index + 1}: Thời gian bắt đầu bán phải nhỏ hơn thời gian kết thúc`
          );
        }

        if (ticket.minQuantityPerPurchase > (ticket.maxQuantityPerPurchase || Infinity)) {
          throw new AppException(
            BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
            `Loại vé thứ ${index + 1}: Số lượng tối thiểu không được lớn hơn số lượng tối đa`
          );
        }
      });
    }
  }

  /**
   * Validate thông tin dịch vụ
   */
  private validateServiceProduct(serviceInfo: ServiceProductDto): void {
    // Validate servicePackages
    if (!serviceInfo.servicePackages || serviceInfo.servicePackages.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Dịch vụ phải có ít nhất một gói dịch vụ'
      );
    }

    // Validate từng service package
    serviceInfo.servicePackages.forEach((pkg, index) => {
      if (pkg.startTime >= pkg.endTime) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Gói dịch vụ thứ ${index + 1}: Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc`
        );
      }

      if (pkg.minQuantityPerPurchase > (pkg.maxQuantityPerPurchase || Infinity)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Gói dịch vụ thứ ${index + 1}: Số lượng tối thiểu không được lớn hơn số lượng tối đa`
        );
      }
    });
  }

  /**
   * Validate thông tin combo sản phẩm
   */
  private validateComboProduct(comboInfo: ComboProductDto): void {
    // Validate info array
    if (!comboInfo.info || !Array.isArray(comboInfo.info) || comboInfo.info.length === 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Combo phải chứa ít nhất 1 sản phẩm'
      );
    }

    // Validate từng item trong combo
    comboInfo.info.forEach((item, index) => {
      if (!item.productId || !item.total) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Sản phẩm thứ ${index + 1} trong combo: productId và total là bắt buộc`
        );
      }

      if (item.productId <= 0 || item.total <= 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
          `Sản phẩm thứ ${index + 1} trong combo: productId và total phải lớn hơn 0`
        );
      }
    });

    // Kiểm tra không có sản phẩm trùng lặp trong combo
    const productIds = comboInfo.info.map(item => item.productId);
    const uniqueProductIds = new Set(productIds);
    if (productIds.length !== uniqueProductIds.size) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Combo không được chứa sản phẩm trùng lặp'
      );
    }
  }
}
