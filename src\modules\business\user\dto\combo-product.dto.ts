import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNumber,
  Min,
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsInt
} from 'class-validator';

/**
 * DTO cho từng item trong combo
 */
export class ComboItemDto {
  @ApiProperty({
    description: 'ID của sản phẩm trong combo',
    example: 123,
    minimum: 1,
  })
  @IsNumber()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  productId: number;

  @ApiProperty({
    description: 'Số lượng sản phẩm trong combo',
    example: 2,
    minimum: 1,
  })
  @IsNumber()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  total: number;
}

/**
 * DTO cho thông tin nâng cao của sản phẩm combo
 */
export class ComboProductDto {
  @ApiProperty({
    description: 'Số lượt mua',
    example: 50,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  purchaseCount: number;

  @ApiProperty({
    description: 'Danh sách sản phẩm trong combo',
    type: [ComboItemDto],
    example: [
      {
        productId: 123,
        total: 2
      },
      {
        productId: 456,
        total: 1
      }
    ],
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'Combo phải chứa ít nhất 1 sản phẩm' })
  @ValidateNested({ each: true })
  @Type(() => ComboItemDto)
  info: ComboItemDto[];
}
