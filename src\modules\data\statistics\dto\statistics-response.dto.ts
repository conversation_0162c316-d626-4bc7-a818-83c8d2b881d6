import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO đại diện cho kết quả thống kê dữ liệu
 * <PERSON><PERSON> gồm số lượng người dùng, knowledge file, media, URL và vector store
 */
export class StatisticsResponseDto {
  @ApiProperty({
    description: 'Tổng số người dùng trong hệ thống',
    example: 1250,
    type: Number,
  })
  @Expose()
  totalUsers: number;

  @ApiProperty({
    description: 'Tổng số file tri thức trong hệ thống',
    example: 350,
    type: Number,
  })
  @Expose()
  totalKnowledgeFiles: number;

  @ApiProperty({
    description: 'Tổng số media trong hệ thống',
    example: 780,
    type: Number,
  })
  @Expose()
  totalMedia: number;

  @ApiProperty({
    description: 'Tổng số URL trong hệ thống',
    example: 420,
    type: Number,
  })
  @Expose()
  totalUrls: number;

  @ApiProperty({
    description: 'Tổng số vector store trong hệ thống',
    example: 85,
    type: Number,
  })
  @Expose()
  totalVectorStores: number;
}

/**
 * DTO đại diện cho chi tiết dung lượng theo từng loại dữ liệu
 */
export class StorageBreakdownDto {
  @ApiProperty({
    description: 'Dung lượng media files (bytes)',
    example: 1073741824,
    type: Number,
  })
  @Expose()
  mediaSize: number;

  @ApiProperty({
    description: 'Dung lượng knowledge files (bytes)',
    example: 536870912,
    type: Number,
  })
  @Expose()
  knowledgeFileSize: number;

  @ApiProperty({
    description: 'Dung lượng vector stores (bytes)',
    example: 268435456,
    type: Number,
  })
  @Expose()
  vectorStoreSize: number;
}

/**
 * DTO đại diện cho kết quả thống kê dung lượng dữ liệu của người dùng
 */
export class UserStorageResponseDto {
  @ApiProperty({
    description: 'Tổng dung lượng được phép sử dụng (bytes)',
    example: 5368709120,
    type: Number,
  })
  @Expose()
  usageLimit: number;

  @ApiProperty({
    description: 'Tổng dung lượng được phép sử dụng (định dạng human-readable)',
    example: '5.00 GB',
    type: String,
  })
  @Expose()
  usageLimitFormatted: string;

  @ApiProperty({
    description: 'Tổng dung lượng đã sử dụng (bytes)',
    example: 1879048192,
    type: Number,
  })
  @Expose()
  currentUsage: number;

  @ApiProperty({
    description: 'Tổng dung lượng đã sử dụng (định dạng human-readable)',
    example: '1.75 GB',
    type: String,
  })
  @Expose()
  currentUsageFormatted: string;

  @ApiProperty({
    description: 'Dung lượng còn lại (bytes)',
    example: 3489660928,
    type: Number,
  })
  @Expose()
  remainingValue: number;

  @ApiProperty({
    description: 'Dung lượng còn lại (định dạng human-readable)',
    example: '3.25 GB',
    type: String,
  })
  @Expose()
  remainingValueFormatted: string;

  @ApiProperty({
    description: 'Đơn vị của tài nguyên',
    example: 'MB',
    type: String,
  })
  @Expose()
  usageUnit: string;

  @ApiProperty({
    description: 'Phần trăm đã sử dụng',
    example: 35.0,
    type: Number,
  })
  @Expose()
  usagePercentage: number;


}
