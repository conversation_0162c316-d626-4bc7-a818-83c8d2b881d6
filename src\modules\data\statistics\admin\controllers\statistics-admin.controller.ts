import { Controller, Get, UseGuards } from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { StatisticsAdminService } from '../services/statistics-admin.service';
import { StatisticsResponseDto } from '../../dto/statistics-response.dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtEmployeeGuard } from '@modules/auth/guards/jwt-employee.guard';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { STATISTICS_ERROR_CODES } from '../../exceptions/statistics.exception';

/**
 * Controller xử lý API admin liên quan đến thống kê dữ liệu
 * Cung cấp endpoint để admin lấy thống kê về số lượng người dùng, knowledge file, media, URL và vector store
 */
@ApiTags(SWAGGER_API_TAGS.DATA_STATISTICS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@ApiExtraModels(ApiResponseDto, StatisticsResponseDto)
@Controller('admin/statistics')
export class StatisticsAdminController {
  constructor(private readonly statisticsAdminService: StatisticsAdminService) {}

  /**
   * Lấy thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store
   * Chỉ admin mới có quyền truy cập endpoint này
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store',
    description: 'API này chỉ dành cho admin để xem thống kê tổng quan của toàn hệ thống bao gồm số lượng users, knowledge files, media files, URLs và vector stores'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê thành công',
    schema: ApiResponseDto.getSchema(StatisticsResponseDto),
  })
  @ApiErrorResponse(
    STATISTICS_ERROR_CODES.STATISTICS_CALCULATION_ERROR,
    STATISTICS_ERROR_CODES.ADMIN_STATISTICS_ONLY
  )
  async getStatistics(): Promise<ApiResponseDto<StatisticsResponseDto>> {
    const statistics = await this.statisticsAdminService.getStatistics();
    return ApiResponseDto.success(statistics, 'Lấy thống kê dữ liệu thành công');
  }
}
