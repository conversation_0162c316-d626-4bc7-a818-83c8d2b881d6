import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { Usage } from '../entities/usage.entity';

/**
 * Repository xử lý các thao tác với bảng usages
 * <PERSON><PERSON> cấp các phương thức để truy vấn thông tin sử dụng tài nguyên của người dùng
 */
@Injectable()
export class UsageRepository extends Repository<Usage> {
  private readonly logger = new Logger(UsageRepository.name);

  constructor(private dataSource: DataSource) {
    super(Usage, dataSource.createEntityManager());
  }

  /**
   * L<PERSON>y thông tin sử dụng tài nguyên của người dùng theo category
   * @param userId ID của người dùng
   * @param category Loại tài nguyên (mặc định là 'DATA')
   * @returns Thông tin sử dụng tài nguyên
   */
  async findByUserAndCategory(userId: number, category: string = 'DATA'): Promise<Usage | null> {
    try {
      this.logger.log(`Đang lấy thông tin usage cho user ${userId}, category: ${category}`);

      const usage = await this.createQueryBuilder('usage')
        .select([
          'usage.id',
          'usage.userId',
          'usage.usageLimit',
          'usage.currentUsage',
          'usage.remainingValue',
          'usage.usageUnit',
          'usage.category',
          'usage.createdAt',
          'usage.updatedAt'
        ])
        .where('usage.userId = :userId', { userId })
        .andWhere('usage.category = :category', { category })
        .getOne();

      if (usage) {
        this.logger.log(`Đã tìm thấy usage cho user ${userId}: limit=${usage.usageLimit}, current=${usage.currentUsage}, remaining=${usage.remainingValue}`);
      } else {
        this.logger.log(`Không tìm thấy usage cho user ${userId}, category: ${category}`);
      }

      return usage;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin usage cho user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy tất cả thông tin sử dụng tài nguyên của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách thông tin sử dụng tài nguyên
   */
  async findAllByUser(userId: number): Promise<Usage[]> {
    try {
      this.logger.log(`Đang lấy tất cả thông tin usage cho user ${userId}`);

      const usages = await this.createQueryBuilder('usage')
        .select([
          'usage.id',
          'usage.userId',
          'usage.usageLimit',
          'usage.currentUsage',
          'usage.remainingValue',
          'usage.usageUnit',
          'usage.category',
          'usage.createdAt',
          'usage.updatedAt'
        ])
        .where('usage.userId = :userId', { userId })
        .orderBy('usage.category', 'ASC')
        .getMany();

      this.logger.log(`Đã tìm thấy ${usages.length} bản ghi usage cho user ${userId}`);

      return usages;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy tất cả thông tin usage cho user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin sử dụng tài nguyên
   * @param userId ID của người dùng
   * @param category Loại tài nguyên
   * @param currentUsage Lượng tài nguyên hiện tại đã sử dụng
   * @returns Thông tin usage đã cập nhật
   */
  async updateCurrentUsage(userId: number, category: string, currentUsage: number): Promise<Usage | null> {
    try {
      this.logger.log(`Đang cập nhật current usage cho user ${userId}, category: ${category}, currentUsage: ${currentUsage}`);

      const usage = await this.findByUserAndCategory(userId, category);
      if (!usage) {
        this.logger.warn(`Không tìm thấy usage để cập nhật cho user ${userId}, category: ${category}`);
        return null;
      }

      // Tính toán remaining value
      const remainingValue = Math.max(0, usage.usageLimit - currentUsage);

      await this.createQueryBuilder()
        .update(Usage)
        .set({
          currentUsage,
          remainingValue,
          updatedAt: () => 'CURRENT_TIMESTAMP'
        })
        .where('userId = :userId', { userId })
        .andWhere('category = :category', { category })
        .execute();

      this.logger.log(`Đã cập nhật usage cho user ${userId}: currentUsage=${currentUsage}, remainingValue=${remainingValue}`);

      // Trả về usage đã cập nhật
      return this.findByUserAndCategory(userId, category);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật usage cho user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
